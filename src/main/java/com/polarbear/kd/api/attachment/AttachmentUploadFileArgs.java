package com.polarbear.kd.api.attachment;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 附件上传参数实体类
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
@Data
public class AttachmentUploadFileArgs {

  /** 源实体标识 如为其他出库单传:"im_otheroutbill"; 如为盘亏单传:"im_deficitbill"; 如为付款申请单传:"ap_payapply"; */
  @JsonProperty("entityNumber")
  private String entityNumber;

  /** 单据主键，即单据Id */
  @JsonProperty("billPkId")
  private Object billPkId;

  /** 单据体行主键 */
  @JsonProperty("entryPkId")
  private Object entryPkId;

  /** 子单据体行主键 */
  @JsonProperty("subEntryPkId")
  private Object subEntryPkId;

  /** 附件控件在扩展表单上添加时需填写扩展表单标识 */
  @JsonProperty("extFormNumber")
  private String extFormNumber;

  /** 附件上传到控件(附件字段、附件面板)的标识 */
  @JsonProperty("controlKey")
  private String controlKey;

  /** 附件备注 */
  @JsonProperty("description")
  private String description;

  /** 应用编码 */
  @JsonProperty("appNumber")
  private String appNumber;
}
