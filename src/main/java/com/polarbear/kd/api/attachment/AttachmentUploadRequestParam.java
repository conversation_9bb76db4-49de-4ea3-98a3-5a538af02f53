package com.polarbear.kd.api.attachment;

import java.io.File;
import lombok.Data;

/**
 * 附件上传请求参数 严格按照接口文档定义，用于multipart/form-data格式的文件上传
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
@Data
public class AttachmentUploadRequestParam {

  /** 上传绑定附件信息 在multipart/form-data中作为JSON字符串传递 */
  private AttachmentUploadFileArgs attachmentUploadFileArgs;

  /** 文件内容 在multipart/form-data中作为文件字段传递 */
  private byte[] file;
}
