package com.polarbear.kd.api.attachment;

import lombok.Data;
import java.io.File;
import java.io.InputStream;

/**
 * 附件上传请求参数
 * 用于multipart/form-data格式的文件上传
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
@Data
public class AttachmentUploadRequestParam {

    /**
     * 上传绑定附件信息（作为JSON字符串传递）
     */
    private AttachmentUploadFileArgs attachmentUploadFileArgs;

    /**
     * 文件对象（支持多种文件类型）
     */
    private File file;

    /**
     * 文件输入流（可选，用于流式上传）
     */
    private InputStream fileInputStream;

    /**
     * 文件名（当使用输入流时需要指定）
     */
    private String fileName;

    /**
     * 文件内容类型（可选）
     */
    private String contentType;
}
