package com.polarbear.kd.api.attachment;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.InputStream;

/**
 * 附件上传辅助类
 * 用于构建multipart/form-data格式的请求参数
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public class AttachmentUploadHelper {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 创建附件上传请求参数（使用本地文件）
     * 
     * @param args 附件上传参数
     * @param file 本地文件
     * @return 请求参数对象
     */
    public static AttachmentUploadRequestParam createRequest(AttachmentUploadFileArgs args, File file) {
        AttachmentUploadRequestParam param = new AttachmentUploadRequestParam();
        param.setAttachmentUploadFileArgs(args);
        param.setFile(file);
        return param;
    }
    
    /**
     * 创建附件上传请求参数（使用输入流）
     * 
     * @param args 附件上传参数
     * @param inputStream 文件输入流
     * @param fileName 文件名
     * @param contentType 文件类型
     * @return 请求参数对象
     */
    public static AttachmentUploadRequestParam createRequest(AttachmentUploadFileArgs args, 
                                                           InputStream inputStream, 
                                                           String fileName, 
                                                           String contentType) {
        AttachmentUploadRequestParam param = new AttachmentUploadRequestParam();
        param.setAttachmentUploadFileArgs(args);
        param.setFileInputStream(inputStream);
        param.setFileName(fileName);
        param.setContentType(contentType);
        return param;
    }
    
    /**
     * 将AttachmentUploadFileArgs转换为JSON字符串
     * 用于multipart/form-data中的文本字段
     * 
     * @param args 附件上传参数
     * @return JSON字符串
     * @throws RuntimeException 转换失败时抛出
     */
    public static String toJsonString(AttachmentUploadFileArgs args) {
        try {
            return objectMapper.writeValueAsString(args);
        } catch (Exception e) {
            throw new RuntimeException("转换AttachmentUploadFileArgs为JSON字符串失败", e);
        }
    }
}
