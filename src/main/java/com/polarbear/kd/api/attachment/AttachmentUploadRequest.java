package com.polarbear.kd.api.attachment;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

/**
 * 附件上传接口
 *
 * <AUTHOR>
 * @apiNote 用于上传附件并绑定附件到对应单据的接口
 * 请求格式为multipart/form-data，包含两个字段：
 * 1. attachmentUploadFileArgs: JSON字符串格式的附件参数
 * 2. file: 文件对象
 * @date 2025-09-02
 */
public class AttachmentUploadRequest extends KdOpRequest<AttachmentUploadRequestParam> {

    @Override
    public String getUrlPath() {
        return "/v2/frame/attachment/uploadFile";
    }

    @Override
    public String logModule() {
        return "kd.frame.attachment.uploadFile";
    }

    @Override
    public Class<AttachmentUploadResponse> getResponseClass() {
        return AttachmentUploadResponse.class;
    }

    @Override
    public boolean standard() {
        // 附件上传接口使用multipart/form-data格式，不使用标准的JSON包装
        return false;
    }

    @Override
    public KdOpRequest<AttachmentUploadRequestParam> setLogKey(Config<AttachmentUploadRequestParam> config) {
        config.setKey1(param -> {
            if (param != null && param.getAttachmentUploadFileArgs() != null) {
                AttachmentUploadFileArgs args = param.getAttachmentUploadFileArgs();
                return args.getEntityNumber() + "_" + args.getBillPkId();
            }
            return "unknown";
        });
        config.setKey2(param -> {
            if (param != null && param.getAttachmentUploadFileArgs() != null) {
                return param.getAttachmentUploadFileArgs().getControlKey();
            }
            return "unknown";
        });
        return this;
    }
}
