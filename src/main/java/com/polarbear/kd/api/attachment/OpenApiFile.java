package com.polarbear.kd.api.attachment;

import lombok.Data;

/**
 * 文件内容实体类
 * 用于表示上传的文件
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Data
public class OpenApiFile {
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件内容类型
     */
    private String contentType;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 文件内容（Base64编码或字节数组）
     */
    private Object fileContent;
    
    /**
     * 文件路径（用于本地文件）
     */
    private String filePath;
}
